-- 农业培训系统表结构设计
-- 创建时间: 2025-07-31
-- 说明: 包含培训类型、讲师管理、课件管理、培训课程、学习记录等核心表

-- 1. 培训类型表
DROP TABLE IF EXISTS `training_category`;
CREATE TABLE `training_category` (
  `category_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `category_name` varchar(50) NOT NULL COMMENT '分类名称',
  `category_code` varchar(30) NOT NULL COMMENT '分类编码',
  `parent_id` bigint(20) DEFAULT 0 COMMENT '父分类ID',
  `ancestors` varchar(500) DEFAULT '' COMMENT '祖级列表',
  `order_num` int(4) DEFAULT 0 COMMENT '显示顺序',
  `status` char(1) DEFAULT '1' COMMENT '状态（1正常 0停用）',
  `icon` varchar(100) DEFAULT '' COMMENT '分类图标',
  `description` varchar(500) DEFAULT '' COMMENT '分类描述',
  `is_hot` char(1) DEFAULT '0' COMMENT '是否热门推荐（1是 0否）',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`category_id`),
  UNIQUE KEY `uk_category_code` (`category_code`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='培训分类表';

-- 2. 讲师管理表
DROP TABLE IF EXISTS `training_instructor`;
CREATE TABLE `training_instructor` (
  `instructor_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '讲师ID',
  `instructor_name` varchar(50) NOT NULL COMMENT '讲师姓名',
  `instructor_code` varchar(30) NOT NULL COMMENT '讲师编号',
  `gender` char(1) DEFAULT '0' COMMENT '性别（0男 1女 2未知）',
  `phone` varchar(20) DEFAULT '' COMMENT '联系电话',
  `email` varchar(100) DEFAULT '' COMMENT '邮箱',
  `id_card` varchar(18) DEFAULT '' COMMENT '身份证号',
  `avatar` varchar(500) DEFAULT '' COMMENT '头像地址',
  `title` varchar(100) DEFAULT '' COMMENT '职称',
  `organization` varchar(200) DEFAULT '' COMMENT '所属机构',
  `specialty` varchar(500) DEFAULT '' COMMENT '专业领域',
  `introduction` text COMMENT '个人简介',
  `experience` text COMMENT '工作经历',
  `achievements` text COMMENT '主要成就',
  `qualification` varchar(500) DEFAULT '' COMMENT '资质证书',
  `level` char(1) DEFAULT '1' COMMENT '讲师级别（1初级 2中级 3高级 4专家）',
  `status` char(1) DEFAULT '1' COMMENT '状态（1正常 0停用）',
  `sort_order` int(4) DEFAULT 0 COMMENT '排序',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`instructor_id`),
  UNIQUE KEY `uk_instructor_code` (`instructor_code`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='培训讲师表';

-- 3. 课件管理表
DROP TABLE IF EXISTS `training_courseware`;
CREATE TABLE `training_courseware` (
  `courseware_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '课件ID',
  `courseware_name` varchar(200) NOT NULL COMMENT '课件名称',
  `courseware_code` varchar(50) NOT NULL COMMENT '课件编号',
  `category_id` bigint(20) NOT NULL COMMENT '分类ID',
  `instructor_id` bigint(20) DEFAULT NULL COMMENT '讲师ID',
  `file_type` varchar(20) NOT NULL COMMENT '文件类型（video视频 audio音频 document文档 image图片）',
  `file_path` varchar(500) NOT NULL COMMENT '文件路径',
  `file_size` bigint(20) DEFAULT 0 COMMENT '文件大小（字节）',
  `duration` int(11) DEFAULT 0 COMMENT '时长（秒）',
  `cover_image` varchar(500) DEFAULT '' COMMENT '封面图片',
  `description` text COMMENT '课件描述',
  `keywords` varchar(500) DEFAULT '' COMMENT '关键词',
  `difficulty_level` char(1) DEFAULT '1' COMMENT '难度级别（1入门 2初级 3中级 4高级）',
  `view_count` int(11) DEFAULT 0 COMMENT '观看次数',
  `download_count` int(11) DEFAULT 0 COMMENT '下载次数',
  `like_count` int(11) DEFAULT 0 COMMENT '点赞数',
  `is_free` char(1) DEFAULT '1' COMMENT '是否免费（1免费 0付费）',
  `price` decimal(10,2) DEFAULT 0.00 COMMENT '价格',
  `status` char(1) DEFAULT '1' COMMENT '状态（1正常 0停用）',
  `is_recommend` char(1) DEFAULT '0' COMMENT '是否推荐（1是 0否）',
  `sort_order` int(4) DEFAULT 0 COMMENT '排序',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`courseware_id`),
  UNIQUE KEY `uk_courseware_code` (`courseware_code`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_instructor_id` (`instructor_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='培训课件表';

-- 4. 培训课程表
DROP TABLE IF EXISTS `training_course`;
CREATE TABLE `training_course` (
  `course_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '课程ID',
  `course_name` varchar(200) NOT NULL COMMENT '课程名称',
  `course_code` varchar(50) NOT NULL COMMENT '课程编号',
  `category_id` bigint(20) NOT NULL COMMENT '分类ID',
  `instructor_id` bigint(20) DEFAULT NULL COMMENT '主讲师ID',
  `cover_image` varchar(500) DEFAULT '' COMMENT '课程封面',
  `description` text COMMENT '课程描述',
  `objectives` text COMMENT '学习目标',
  `outline` text COMMENT '课程大纲',
  `difficulty_level` char(1) DEFAULT '1' COMMENT '难度级别（1入门 2初级 3中级 4高级）',
  `total_duration` int(11) DEFAULT 0 COMMENT '总时长（分钟）',
  `lesson_count` int(11) DEFAULT 0 COMMENT '课时数量',
  `is_free` char(1) DEFAULT '1' COMMENT '是否免费（1免费 0付费）',
  `price` decimal(10,2) DEFAULT 0.00 COMMENT '价格',
  `original_price` decimal(10,2) DEFAULT 0.00 COMMENT '原价',
  `enrollment_count` int(11) DEFAULT 0 COMMENT '报名人数',
  `completion_count` int(11) DEFAULT 0 COMMENT '完成人数',
  `rating` decimal(3,2) DEFAULT 0.00 COMMENT '评分',
  `rating_count` int(11) DEFAULT 0 COMMENT '评分人数',
  `start_time` datetime DEFAULT NULL COMMENT '开课时间',
  `end_time` datetime DEFAULT NULL COMMENT '结课时间',
  `enrollment_start` datetime DEFAULT NULL COMMENT '报名开始时间',
  `enrollment_end` datetime DEFAULT NULL COMMENT '报名结束时间',
  `max_students` int(11) DEFAULT 0 COMMENT '最大学员数（0表示不限制）',
  `status` char(1) DEFAULT '1' COMMENT '状态（1正常 0停用 2已结束）',
  `is_recommend` char(1) DEFAULT '0' COMMENT '是否推荐（1是 0否）',
  `sort_order` int(4) DEFAULT 0 COMMENT '排序',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`course_id`),
  UNIQUE KEY `uk_course_code` (`course_code`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_instructor_id` (`instructor_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='培训课程表';

-- 5. 课程课件关联表
DROP TABLE IF EXISTS `training_course_courseware`;
CREATE TABLE `training_course_courseware` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `course_id` bigint(20) NOT NULL COMMENT '课程ID',
  `courseware_id` bigint(20) NOT NULL COMMENT '课件ID',
  `lesson_order` int(4) DEFAULT 0 COMMENT '课时顺序',
  `lesson_name` varchar(200) DEFAULT '' COMMENT '课时名称',
  `is_required` char(1) DEFAULT '1' COMMENT '是否必修（1是 0否）',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_course_courseware` (`course_id`,`courseware_id`),
  KEY `idx_course_id` (`course_id`),
  KEY `idx_courseware_id` (`courseware_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='课程课件关联表';

-- 6. 学习记录表
DROP TABLE IF EXISTS `training_study_record`;
CREATE TABLE `training_study_record` (
  `record_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `course_id` bigint(20) DEFAULT NULL COMMENT '课程ID',
  `courseware_id` bigint(20) DEFAULT NULL COMMENT '课件ID',
  `study_type` char(1) NOT NULL COMMENT '学习类型（1课程 2课件）',
  `study_progress` decimal(5,2) DEFAULT 0.00 COMMENT '学习进度（百分比）',
  `study_duration` int(11) DEFAULT 0 COMMENT '学习时长（秒）',
  `last_position` int(11) DEFAULT 0 COMMENT '最后学习位置（秒）',
  `is_completed` char(1) DEFAULT '0' COMMENT '是否完成（1是 0否）',
  `completion_time` datetime DEFAULT NULL COMMENT '完成时间',
  `score` decimal(5,2) DEFAULT NULL COMMENT '考试成绩',
  `certificate_url` varchar(500) DEFAULT '' COMMENT '证书地址',
  `study_start_time` datetime DEFAULT NULL COMMENT '开始学习时间',
  `study_end_time` datetime DEFAULT NULL COMMENT '结束学习时间',
  `device_type` varchar(20) DEFAULT '' COMMENT '设备类型（pc web mobile app）',
  `ip_address` varchar(50) DEFAULT '' COMMENT 'IP地址',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`record_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_course_id` (`course_id`),
  KEY `idx_courseware_id` (`courseware_id`),
  KEY `idx_study_type` (`study_type`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='学习记录表';

-- 7. 课程评价表
DROP TABLE IF EXISTS `training_course_evaluation`;
CREATE TABLE `training_course_evaluation` (
  `evaluation_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '评价ID',
  `course_id` bigint(20) NOT NULL COMMENT '课程ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `rating` decimal(3,2) NOT NULL COMMENT '评分（1-5分）',
  `content` text COMMENT '评价内容',
  `is_anonymous` char(1) DEFAULT '0' COMMENT '是否匿名（1是 0否）',
  `like_count` int(11) DEFAULT 0 COMMENT '点赞数',
  `status` char(1) DEFAULT '1' COMMENT '状态（1正常 0隐藏）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`evaluation_id`),
  UNIQUE KEY `uk_course_user` (`course_id`,`user_id`),
  KEY `idx_course_id` (`course_id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='课程评价表';

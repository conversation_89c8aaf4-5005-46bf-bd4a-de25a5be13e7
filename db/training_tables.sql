-- 农业培训系统表结构设计
-- 创建时间: 2025-07-31
-- 说明: 包含讲师管理、课件管理、学习记录等核心表

-- 1. 讲师管理表
DROP TABLE IF EXISTS `t_business_training_instructor`;
CREATE TABLE `t_business_training_instructor` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '讲师主键ID',
  `instructor_name` varchar(50) NOT NULL COMMENT '讲师姓名',
  `instructor_no` varchar(30) NOT NULL COMMENT '讲师编号',
  `instructor_phone` varchar(20) DEFAULT '' COMMENT '讲师联系电话',
  `instructor_avatar` varchar(500) DEFAULT '' COMMENT '讲师头像地址',
  `instructor_title` varchar(100) DEFAULT '' COMMENT '讲师职称',
  `instructor_organization` varchar(200) DEFAULT '' COMMENT '讲师所属机构',
  `instructor_specialty` varchar(200) DEFAULT '' COMMENT '讲师专业领域',
  `instructor_introduction` varchar(1000) DEFAULT '' COMMENT '讲师个人简介',
  `status` char(1) DEFAULT '1' COMMENT '状态（1正常 0停用）',
  `delete_flag` char(1) DEFAULT '0' COMMENT '删除标志（0存在 1删除）',
  `tenant_id` varchar(20) DEFAULT '' COMMENT '租户编号',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注信息',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_ti_instructor_no` (`instructor_no`),
  KEY `idx_ti_status` (`status`),
  KEY `idx_ti_tenant_id` (`tenant_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='农业培训讲师信息表';

-- 2. 课件管理表
DROP TABLE IF EXISTS `t_business_training_courseware`;
CREATE TABLE `t_business_training_courseware` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '课件ID',
  `courseware_name` varchar(200) NOT NULL COMMENT '课件名称',
  `courseware_no` varchar(50) NOT NULL COMMENT '课件编号',
  `category_type` varchar(30) NOT NULL COMMENT '分类类型（hot_recommend热门推荐 planting_tech种植技术 business_mgmt经营管理 expert_course专家课程 policy_explain政策解读 wealth_experience致富经验）',
  `instructor_id` bigint(20) DEFAULT NULL COMMENT '讲师ID',
  `file_type` varchar(20) NOT NULL COMMENT '文件类型（video视频 audio音频 document文档 image图片）',
  `file_paths` text COMMENT '文件路径（JSON格式存储多个文件）',
  `cover_image` varchar(500) DEFAULT '' COMMENT '封面图片',
  `description` varchar(1000) DEFAULT '' COMMENT '课件描述',
  `view_count` int(11) DEFAULT 0 COMMENT '观看次数',
  `like_count` int(11) DEFAULT 0 COMMENT '点赞数',
  `status` char(1) DEFAULT '1' COMMENT '状态（1正常 0停用）',
  `is_recommend` char(1) DEFAULT '0' COMMENT '是否推荐（1是 0否）',
  `sort_order` int(4) DEFAULT 0 COMMENT '排序',
  `delete_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 1代表删除）',
  `tenant_id` varchar(20) DEFAULT '' COMMENT '租户ID',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_tc_courseware_no` (`courseware_no`),
  KEY `idx_tc_category_type` (`category_type`),
  KEY `idx_tc_instructor_id` (`instructor_id`),
  KEY `idx_tc_status` (`status`),
  KEY `idx_tc_is_recommend` (`is_recommend`),
  KEY `idx_tc_tenant_id` (`tenant_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='培训课件表';

-- 3. 学习记录表
DROP TABLE IF EXISTS `t_business_training_study_record`;
CREATE TABLE `t_business_training_study_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `courseware_id` bigint(20) NOT NULL COMMENT '课件ID',
  `study_progress` decimal(5,2) DEFAULT 0.00 COMMENT '学习进度（百分比）',
  `last_position` int(11) DEFAULT 0 COMMENT '最后学习位置（秒）',
  `is_completed` char(1) DEFAULT '0' COMMENT '是否完成（1是 0否）',
  `completion_time` datetime DEFAULT NULL COMMENT '完成时间',
  `study_start_time` datetime DEFAULT NULL COMMENT '开始学习时间',
  `tenant_id` varchar(20) DEFAULT '' COMMENT '租户ID',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_tsr_user_id` (`user_id`),
  KEY `idx_tsr_courseware_id` (`courseware_id`),
  KEY `idx_tsr_user_courseware` (`user_id`, `courseware_id`),
  KEY `idx_tsr_completed` (`is_completed`),
  KEY `idx_tsr_tenant_id` (`tenant_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='学习记录表';

-- 初始化字典数据
-- 培训分类字典类型
INSERT INTO `sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `remark`) VALUES
('培训分类', 'training_category_type', '1', 'admin', NOW(), '农业培训分类类型');

-- 培训分类字典数据
INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `remark`) VALUES
(1, '热门推荐', 'hot_recommend', 'training_category_type', '', 'danger', 'Y', '1', 'admin', NOW(), '热门推荐课程'),
(2, '种植技术', 'planting_tech', 'training_category_type', '', 'success', 'N', '1', 'admin', NOW(), '农作物种植技术培训'),
(3, '经营管理', 'business_mgmt', 'training_category_type', '', 'info', 'N', '1', 'admin', NOW(), '农业经营管理培训'),
(4, '专家课程', 'expert_course', 'training_category_type', '', 'warning', 'N', '1', 'admin', NOW(), '专家授课课程'),
(5, '政策解读', 'policy_explain', 'training_category_type', '', 'primary', 'N', '1', 'admin', NOW(), '农业政策解读'),
(6, '致富经验', 'wealth_experience', 'training_category_type', '', 'default', 'N', '1', 'admin', NOW(), '致富经验分享');



-- 课件文件类型字典类型
INSERT INTO `sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `remark`) VALUES
('课件文件类型', 'training_file_type', '1', 'admin', NOW(), '培训课件文件类型');

-- 课件文件类型字典数据
INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `remark`) VALUES
(1, '视频', 'video', 'training_file_type', '', 'danger', 'Y', '1', 'admin', NOW(), '视频课件'),
(2, '音频', 'audio', 'training_file_type', '', 'warning', 'N', '1', 'admin', NOW(), '音频课件'),
(3, '文档', 'document', 'training_file_type', '', 'info', 'N', '1', 'admin', NOW(), '文档课件'),
(4, '图片', 'image', 'training_file_type', '', 'success', 'N', '1', 'admin', NOW(), '图片课件');





# 农业培训系统数据库设计文档

## 概述

本文档描述了农业培训系统的数据库表结构设计，包含讲师管理、课件管理、学习记录等核心功能模块。培训分类通过字典数据管理，支持热门推荐、种植技术、经营管理、专家课程、政策解读、致富经验等分类。

## 表结构设计

### 1. 讲师管理表 (training_instructor)

**表名**: `training_instructor`
**说明**: 管理培训讲师的基本信息、专业领域、资质等。

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint | 20 | 是 | 自增 | 讲师ID（主键） |
| instructor_name | varchar | 50 | 是 | - | 讲师姓名 |
| instructor_code | varchar | 30 | 是 | - | 讲师编号（唯一） |
| gender | char | 1 | 否 | '0' | 性别（0男 1女 2未知） |
| phone | varchar | 20 | 否 | '' | 联系电话 |
| email | varchar | 100 | 否 | '' | 邮箱 |
| id_card | varchar | 18 | 否 | '' | 身份证号 |
| avatar | varchar | 500 | 否 | '' | 头像地址 |
| title | varchar | 100 | 否 | '' | 职称 |
| organization | varchar | 200 | 否 | '' | 所属机构 |
| specialty | varchar | 500 | 否 | '' | 专业领域 |
| introduction | text | - | 否 | - | 个人简介 |
| experience | text | - | 否 | - | 工作经历 |
| achievements | text | - | 否 | - | 主要成就 |
| qualification | varchar | 500 | 否 | '' | 资质证书 |
| level | char | 1 | 否 | '1' | 讲师级别（1初级 2中级 3高级 4专家） |
| status | char | 1 | 否 | '1' | 状态（1正常 0停用） |
| sort_order | int | 4 | 否 | 0 | 排序 |

**索引**:
- 主键索引: `id`
- 唯一索引: `instructor_code`
- 普通索引: `status`, `level`, `organization`

### 2. 课件管理表 (training_courseware)

**表名**: `training_courseware`
**说明**: 管理培训课件资源，支持视频、音频、文档、图片等多种格式。

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint | 20 | 是 | 自增 | 课件ID（主键） |
| courseware_name | varchar | 200 | 是 | - | 课件名称 |
| courseware_code | varchar | 50 | 是 | - | 课件编号（唯一） |
| category_type | varchar | 30 | 是 | - | 分类类型（字典值） |
| instructor_id | bigint | 20 | 否 | - | 讲师ID |
| file_type | varchar | 20 | 是 | - | 文件类型（video/audio/document/image） |
| file_path | varchar | 500 | 是 | - | 文件路径 |
| file_size | bigint | 20 | 否 | 0 | 文件大小（字节） |
| duration | int | 11 | 否 | 0 | 时长（秒） |
| cover_image | varchar | 500 | 否 | '' | 封面图片 |
| description | text | - | 否 | - | 课件描述 |
| keywords | varchar | 500 | 否 | '' | 关键词 |
| difficulty_level | char | 1 | 否 | '1' | 难度级别（1入门 2初级 3中级 4高级） |
| view_count | int | 11 | 否 | 0 | 观看次数 |
| download_count | int | 11 | 否 | 0 | 下载次数 |
| like_count | int | 11 | 否 | 0 | 点赞数 |
| is_free | char | 1 | 否 | '1' | 是否免费（1免费 0付费） |
| price | decimal | 10,2 | 否 | 0.00 | 价格 |
| status | char | 1 | 否 | '1' | 状态（1正常 0停用） |
| is_recommend | char | 1 | 否 | '0' | 是否推荐（1是 0否） |
| sort_order | int | 4 | 否 | 0 | 排序 |

**索引**:
- 主键索引: `id`
- 唯一索引: `courseware_code`
- 普通索引: `category_type`, `instructor_id`, `status`, `file_type`, `is_recommend`, `difficulty_level`

### 3. 学习记录表 (training_study_record)

**表名**: `training_study_record`
**说明**: 记录用户的学习情况，包含学习进度、时长、完成状态等。

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint | 20 | 是 | 自增 | 记录ID（主键） |
| user_id | bigint | 20 | 是 | - | 用户ID |
| courseware_id | bigint | 20 | 是 | - | 课件ID |
| study_progress | decimal | 5,2 | 否 | 0.00 | 学习进度（百分比） |
| study_duration | int | 11 | 否 | 0 | 学习时长（秒） |
| last_position | int | 11 | 否 | 0 | 最后学习位置（秒） |
| is_completed | char | 1 | 否 | '0' | 是否完成（1是 0否） |
| completion_time | datetime | - | 否 | - | 完成时间 |
| score | decimal | 5,2 | 否 | - | 考试成绩 |
| certificate_url | varchar | 500 | 否 | '' | 证书地址 |
| study_start_time | datetime | - | 否 | - | 开始学习时间 |
| study_end_time | datetime | - | 否 | - | 结束学习时间 |
| device_type | varchar | 20 | 否 | '' | 设备类型（pc/web/mobile/app） |
| ip_address | varchar | 50 | 否 | '' | IP地址 |

**索引**:
- 主键索引: `id`
- 普通索引: `user_id`, `courseware_id`, `user_id,courseware_id`, `is_completed`, `create_time`

## 字典数据配置

系统通过字典数据管理各种分类和枚举值：

### 培训分类 (training_category_type)
- **热门推荐** (hot_recommend) - 热门推荐课程
- **种植技术** (planting_tech) - 农作物种植技术培训
- **经营管理** (business_mgmt) - 农业经营管理培训
- **专家课程** (expert_course) - 专家授课课程
- **政策解读** (policy_explain) - 农业政策解读
- **致富经验** (wealth_experience) - 致富经验分享

### 讲师级别 (training_instructor_level)
- **初级** (1) - 初级讲师
- **中级** (2) - 中级讲师
- **高级** (3) - 高级讲师
- **专家** (4) - 专家级讲师

### 课件文件类型 (training_file_type)
- **视频** (video) - 视频课件
- **音频** (audio) - 音频课件
- **文档** (document) - 文档课件
- **图片** (image) - 图片课件

### 难度级别 (training_difficulty_level)
- **入门** (1) - 入门级别
- **初级** (2) - 初级级别
- **中级** (3) - 中级级别
- **高级** (4) - 高级级别

### 设备类型 (training_device_type)
- **PC端** (pc) - PC电脑端
- **网页端** (web) - 网页浏览器
- **手机端** (mobile) - 手机移动端
- **APP端** (app) - 手机APP应用

## 设计特点

1. **遵循项目规范**:
   - 主键统一使用 `id`
   - 继承BaseEntity基类，包含通用字段（create_by, create_time, update_by, update_time, remark）
   - 遵循项目的命名规范和字段类型

2. **字典数据管理**:
   - 培训分类通过字典数据管理，便于维护和扩展
   - 各种枚举值统一使用字典配置

3. **简化的表结构**:
   - 去掉了复杂的课程表和关联表
   - 直接通过课件进行学习记录跟踪
   - 结构更加简洁清晰

4. **完整的学习跟踪**:
   - 记录学习进度、时长、完成状态等详细信息
   - 支持断点续学功能
   - 记录学习设备和IP信息

5. **性能优化**:
   - 索引直接集成在建表语句中
   - 创建了必要的复合索引提升查询性能

6. **扩展性**:
   - 预留了足够的字段支持未来功能扩展
   - 支持多种课件类型和难度级别

# 农业培训系统数据库设计文档

## 概述

本文档描述了农业培训系统的数据库表结构设计，包含培训分类、讲师管理、课件管理、培训课程、学习记录等核心功能模块。

## 表结构设计

### 1. 培训分类表 (training_category)

**表名**: `training_category`  
**说明**: 管理培训课程的分类，支持树形结构，包含热门推荐、种植技术、经营管理、专家课程、政策解读、致富经验等分类。

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint | 20 | 是 | 自增 | 分类ID（主键） |
| category_name | varchar | 50 | 是 | - | 分类名称 |
| category_code | varchar | 30 | 是 | - | 分类编码（唯一） |
| parent_id | bigint | 20 | 否 | 0 | 父分类ID |
| ancestors | varchar | 500 | 否 | '' | 祖级列表 |
| order_num | int | 4 | 否 | 0 | 显示顺序 |
| status | char | 1 | 否 | '1' | 状态（1正常 0停用） |
| icon | varchar | 100 | 否 | '' | 分类图标 |
| description | varchar | 500 | 否 | '' | 分类描述 |
| is_hot | char | 1 | 否 | '0' | 是否热门推荐（1是 0否） |

**索引**:
- 主键索引: `id`
- 唯一索引: `category_code`
- 普通索引: `parent_id`, `status`, `is_hot`

### 2. 讲师管理表 (training_instructor)

**表名**: `training_instructor`  
**说明**: 管理培训讲师的基本信息、专业领域、资质等。

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint | 20 | 是 | 自增 | 讲师ID（主键） |
| instructor_name | varchar | 50 | 是 | - | 讲师姓名 |
| instructor_code | varchar | 30 | 是 | - | 讲师编号（唯一） |
| gender | char | 1 | 否 | '0' | 性别（0男 1女 2未知） |
| phone | varchar | 20 | 否 | '' | 联系电话 |
| email | varchar | 100 | 否 | '' | 邮箱 |
| id_card | varchar | 18 | 否 | '' | 身份证号 |
| avatar | varchar | 500 | 否 | '' | 头像地址 |
| title | varchar | 100 | 否 | '' | 职称 |
| organization | varchar | 200 | 否 | '' | 所属机构 |
| specialty | varchar | 500 | 否 | '' | 专业领域 |
| introduction | text | - | 否 | - | 个人简介 |
| experience | text | - | 否 | - | 工作经历 |
| achievements | text | - | 否 | - | 主要成就 |
| qualification | varchar | 500 | 否 | '' | 资质证书 |
| level | char | 1 | 否 | '1' | 讲师级别（1初级 2中级 3高级 4专家） |
| status | char | 1 | 否 | '1' | 状态（1正常 0停用） |
| sort_order | int | 4 | 否 | 0 | 排序 |

**索引**:
- 主键索引: `id`
- 唯一索引: `instructor_code`
- 普通索引: `status`, `level`, `organization`

### 3. 课件管理表 (training_courseware)

**表名**: `training_courseware`  
**说明**: 管理培训课件资源，支持视频、音频、文档、图片等多种格式。

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint | 20 | 是 | 自增 | 课件ID（主键） |
| courseware_name | varchar | 200 | 是 | - | 课件名称 |
| courseware_code | varchar | 50 | 是 | - | 课件编号（唯一） |
| category_id | bigint | 20 | 是 | - | 分类ID |
| instructor_id | bigint | 20 | 否 | - | 讲师ID |
| file_type | varchar | 20 | 是 | - | 文件类型（video/audio/document/image） |
| file_path | varchar | 500 | 是 | - | 文件路径 |
| file_size | bigint | 20 | 否 | 0 | 文件大小（字节） |
| duration | int | 11 | 否 | 0 | 时长（秒） |
| cover_image | varchar | 500 | 否 | '' | 封面图片 |
| description | text | - | 否 | - | 课件描述 |
| keywords | varchar | 500 | 否 | '' | 关键词 |
| difficulty_level | char | 1 | 否 | '1' | 难度级别（1入门 2初级 3中级 4高级） |
| view_count | int | 11 | 否 | 0 | 观看次数 |
| download_count | int | 11 | 否 | 0 | 下载次数 |
| like_count | int | 11 | 否 | 0 | 点赞数 |
| is_free | char | 1 | 否 | '1' | 是否免费（1免费 0付费） |
| price | decimal | 10,2 | 否 | 0.00 | 价格 |
| status | char | 1 | 否 | '1' | 状态（1正常 0停用） |
| is_recommend | char | 1 | 否 | '0' | 是否推荐（1是 0否） |
| sort_order | int | 4 | 否 | 0 | 排序 |

**索引**:
- 主键索引: `id`
- 唯一索引: `courseware_code`
- 普通索引: `category_id`, `instructor_id`, `status`, `file_type`, `is_recommend`, `difficulty_level`

### 4. 培训课程表 (training_course)

**表名**: `training_course`  
**说明**: 管理培训课程信息，包含课程基本信息、报名信息、评价统计等。

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint | 20 | 是 | 自增 | 课程ID（主键） |
| course_name | varchar | 200 | 是 | - | 课程名称 |
| course_code | varchar | 50 | 是 | - | 课程编号（唯一） |
| category_id | bigint | 20 | 是 | - | 分类ID |
| instructor_id | bigint | 20 | 否 | - | 主讲师ID |
| cover_image | varchar | 500 | 否 | '' | 课程封面 |
| description | text | - | 否 | - | 课程描述 |
| objectives | text | - | 否 | - | 学习目标 |
| outline | text | - | 否 | - | 课程大纲 |
| difficulty_level | char | 1 | 否 | '1' | 难度级别（1入门 2初级 3中级 4高级） |
| total_duration | int | 11 | 否 | 0 | 总时长（分钟） |
| lesson_count | int | 11 | 否 | 0 | 课时数量 |
| is_free | char | 1 | 否 | '1' | 是否免费（1免费 0付费） |
| price | decimal | 10,2 | 否 | 0.00 | 价格 |
| original_price | decimal | 10,2 | 否 | 0.00 | 原价 |
| enrollment_count | int | 11 | 否 | 0 | 报名人数 |
| completion_count | int | 11 | 否 | 0 | 完成人数 |
| rating | decimal | 3,2 | 否 | 0.00 | 评分 |
| rating_count | int | 11 | 否 | 0 | 评分人数 |
| start_time | datetime | - | 否 | - | 开课时间 |
| end_time | datetime | - | 否 | - | 结课时间 |
| enrollment_start | datetime | - | 否 | - | 报名开始时间 |
| enrollment_end | datetime | - | 否 | - | 报名结束时间 |
| max_students | int | 11 | 否 | 0 | 最大学员数（0表示不限制） |
| status | char | 1 | 否 | '1' | 状态（1正常 0停用 2已结束） |
| is_recommend | char | 1 | 否 | '0' | 是否推荐（1是 0否） |
| sort_order | int | 4 | 否 | 0 | 排序 |

**索引**:
- 主键索引: `id`
- 唯一索引: `course_code`
- 普通索引: `category_id`, `instructor_id`, `status`, `is_recommend`, `difficulty_level`, `start_time`, `enrollment_start,enrollment_end`

### 5. 课程课件关联表 (training_course_courseware)

**表名**: `training_course_courseware`  
**说明**: 管理课程与课件的关联关系，支持课时排序。

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint | 20 | 是 | 自增 | 主键ID |
| course_id | bigint | 20 | 是 | - | 课程ID |
| courseware_id | bigint | 20 | 是 | - | 课件ID |
| lesson_order | int | 4 | 否 | 0 | 课时顺序 |
| lesson_name | varchar | 200 | 否 | '' | 课时名称 |
| is_required | char | 1 | 否 | '1' | 是否必修（1是 0否） |
| create_time | datetime | - | 否 | - | 创建时间 |

**索引**:
- 主键索引: `id`
- 唯一索引: `course_id,courseware_id`
- 普通索引: `course_id`, `courseware_id`

### 6. 学习记录表 (training_study_record)

**表名**: `training_study_record`  
**说明**: 记录用户的学习情况，包含学习进度、时长、完成状态等。

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint | 20 | 是 | 自增 | 记录ID（主键） |
| user_id | bigint | 20 | 是 | - | 用户ID |
| course_id | bigint | 20 | 否 | - | 课程ID |
| courseware_id | bigint | 20 | 否 | - | 课件ID |
| study_type | char | 1 | 是 | - | 学习类型（1课程 2课件） |
| study_progress | decimal | 5,2 | 否 | 0.00 | 学习进度（百分比） |
| study_duration | int | 11 | 否 | 0 | 学习时长（秒） |
| last_position | int | 11 | 否 | 0 | 最后学习位置（秒） |
| is_completed | char | 1 | 否 | '0' | 是否完成（1是 0否） |
| completion_time | datetime | - | 否 | - | 完成时间 |
| score | decimal | 5,2 | 否 | - | 考试成绩 |
| certificate_url | varchar | 500 | 否 | '' | 证书地址 |
| study_start_time | datetime | - | 否 | - | 开始学习时间 |
| study_end_time | datetime | - | 否 | - | 结束学习时间 |
| device_type | varchar | 20 | 否 | '' | 设备类型（pc/web/mobile/app） |
| ip_address | varchar | 50 | 否 | '' | IP地址 |

**索引**:
- 主键索引: `id`
- 普通索引: `user_id`, `course_id`, `courseware_id`, `study_type`, `user_id,course_id`, `user_id,courseware_id`, `is_completed`, `create_time`

### 7. 课程评价表 (training_course_evaluation)

**表名**: `training_course_evaluation`  
**说明**: 管理用户对课程的评价和评分。

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint | 20 | 是 | 自增 | 评价ID（主键） |
| course_id | bigint | 20 | 是 | - | 课程ID |
| user_id | bigint | 20 | 是 | - | 用户ID |
| rating | decimal | 3,2 | 是 | - | 评分（1-5分） |
| content | text | - | 否 | - | 评价内容 |
| is_anonymous | char | 1 | 否 | '0' | 是否匿名（1是 0否） |
| like_count | int | 11 | 否 | 0 | 点赞数 |
| status | char | 1 | 否 | '1' | 状态（1正常 0隐藏） |

**索引**:
- 主键索引: `id`
- 唯一索引: `course_id,user_id`
- 普通索引: `course_id`, `user_id`, `rating`, `status`, `create_time`

## 初始化数据

系统预置了以下培训分类：

1. **热门推荐** - 热门推荐课程
2. **种植技术** - 农作物种植技术培训
   - 粮食作物 - 水稻、小麦、玉米等
   - 经济作物 - 棉花、油菜、甘蔗等
   - 蔬菜种植 - 各类蔬菜种植技术
   - 果树栽培 - 果树种植与管理
3. **经营管理** - 农业经营管理培训
   - 合作社管理 - 农民专业合作社管理
   - 农产品营销 - 农产品销售与营销
   - 财务管理 - 农业财务管理
4. **专家课程** - 专家授课课程
5. **政策解读** - 农业政策解读
6. **致富经验** - 致富经验分享

## 设计特点

1. **遵循项目规范**: 继承BaseEntity基类，包含通用字段（create_by, create_time, update_by, update_time, remark）
2. **支持树形结构**: 培训分类支持多级分类
3. **灵活的课程组织**: 课程可以包含多个课件，支持课时排序
4. **完整的学习跟踪**: 记录学习进度、时长、完成状态等详细信息
5. **评价体系**: 支持课程评价和评分统计
6. **性能优化**: 创建了必要的索引提升查询性能
7. **扩展性**: 预留了足够的字段支持未来功能扩展
